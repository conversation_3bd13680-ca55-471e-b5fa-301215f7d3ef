api = "0.7"

[buildpack]
  description = "A buildpack for building Go applications"
  homepage = "https://github.com/paketo-buildpacks/go"
  id = "paketo-buildpacks/go"
  keywords = ["go", "golang"]
  name = "Paketo Buildpack for Go"

  [[buildpack.licenses]]
    type = "Apache-2.0"
    uri = "https://github.com/paketo-buildpacks/go/blob/main/LICENSE"

[metadata]
  include-files = ["buildpack.toml"]

[[order]]

  [[order.group]]
    id = "paketo-buildpacks/ca-certificates"
    optional = true
    version = "3.10.3"

  [[order.group]]
    id = "paketo-buildpacks/watchexec"
    optional = true
    version = "3.5.3"

  [[order.group]]
    id = "paketo-buildpacks/go-dist"
    version = "2.7.14"

  [[order.group]]
    id = "paketo-buildpacks/git"
    optional = true
    version = "1.0.61"

  [[order.group]]
    id = "paketo-buildpacks/go-mod-vendor"
    version = "1.0.69"

  [[order.group]]
    id = "paketo-buildpacks/go-build"
    version = "2.2.40"

  [[order.group]]
    id = "paketo-buildpacks/procfile"
    optional = true
    version = "5.11.2"

  [[order.group]]
    id = "paketo-buildpacks/environment-variables"
    optional = true
    version = "4.9.2"

  [[order.group]]
    id = "paketo-buildpacks/image-labels"
    optional = true
    version = "4.10.1"

[[order]]

  [[order.group]]
    id = "paketo-buildpacks/ca-certificates"
    optional = true
    version = "3.10.3"

  [[order.group]]
    id = "paketo-buildpacks/watchexec"
    optional = true
    version = "3.5.3"

  [[order.group]]
    id = "paketo-buildpacks/go-dist"
    version = "2.7.14"

  [[order.group]]
    id = "paketo-buildpacks/git"
    optional = true
    version = "1.0.61"

  [[order.group]]
    id = "paketo-buildpacks/go-build"
    version = "2.2.40"

  [[order.group]]
    id = "paketo-buildpacks/procfile"
    optional = true
    version = "5.11.2"

  [[order.group]]
    id = "paketo-buildpacks/environment-variables"
    optional = true
    version = "4.9.2"

  [[order.group]]
    id = "paketo-buildpacks/image-labels"
    optional = true
    version = "4.10.1"
