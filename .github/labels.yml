- name: status/possible-priority
  description: This issue is ready to work and should be considered as a potential priority
  color: F9D0C4
- name: status/prioritized
  description: This issue has been triaged and resolving it is a priority
  color: BFD4F2
- name: status/blocked
  description: This issue has been triaged and resolving it is blocked on some other issue
  color: 848978
- name: bug
  description: Something isn't working
  color: d73a4a
- name: enhancement
  description: A new feature or request
  color: a2eeef
- name: documentation
  description: This issue relates to writing documentation
  color: D4C5F9
- name: help wanted
  description: Extra attention is needed
  color: 008672
- name: semver:major
  description: A change requiring a major version bump
  color: 6b230e
- name: semver:minor
  description: A change requiring a minor version bump
  color: cc6749
- name: semver:patch
  description: A change requiring a patch version bump
  color: f9d0c4
- name: good first issue
  description: A good first issue to get started with
  color: d3fc03
- name: "failure:release"
  description: An issue filed automatically when a release workflow run fails
  color: f00a0a
- name: "failure:push"
  description: An issue filed automatically when a push buildpackage workflow run fails
  color: f00a0a
- name: "failure:update-buildpack-toml"
  description: An issue filed automatically when a buildpack.toml update workflow run fails
  color: f00a0a
- name: "failure:update-github-config"
  description: An issue filed automatically when a github config update workflow run fails
  color: f00a0a
- name: "failure:approve-bot-pr"
  description: An issue filed automatically when a PR auto-approve workflow run fails
  color: f00a0a
