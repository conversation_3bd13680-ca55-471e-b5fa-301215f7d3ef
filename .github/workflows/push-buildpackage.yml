name: Push Buildpackage

on:
  release:
    types:
    - published
env:
  REGISTRIES_FILENAME: "registries.json"

jobs:
  push:
    name: Push
    runs-on: ubuntu-24.04
    steps:

    - name: Checkout
      uses: actions/checkout@v4

    - name: Parse Event
      id: event
      run: |
        FULL_VERSION="$(jq -r '.release.tag_name' "${GITHUB_EVENT_PATH}" | sed s/^v//)"
        MINOR_VERSION="$(echo "${FULL_VERSION}" | awk -F '.' '{print $1 "." $2 }')"
        MAJOR_VERSION="$(echo "${FULL_VERSION}" | awk -F '.' '{print $1 }')"
        echo "tag_full=${FULL_VERSION}" >> "$GITHUB_OUTPUT"
        echo "tag_minor=${MINOR_VERSION}" >> "$GITHUB_OUTPUT"
        echo "tag_major=${MAJOR_VERSION}" >> "$GITHUB_OUTPUT"
        echo "download_url=$(jq -r '.release.assets[] | select(.name | endswith(".cnb")) | .url' "${GITHUB_EVENT_PATH}")" >> "$GITHUB_OUTPUT"

    - name: Download
      id: download
      uses: paketo-buildpacks/github-config/actions/release/download-asset@main
      with:
        url: ${{ steps.event.outputs.download_url }}
        output: "/github/workspace/buildpackage.cnb"
        token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}

    - name: Parse Configs
      id: parse_configs
      run: |
        registries_filename="${{ env.REGISTRIES_FILENAME }}"

        push_to_dockerhub=true
        push_to_gcr=false

        if [[ -f $registries_filename ]]; then
          if jq 'has("dockerhub")' $registries_filename > /dev/null; then
            push_to_dockerhub=$(jq '.dockerhub' $registries_filename)
          fi
          if jq 'has("GCR")' $registries_filename > /dev/null; then
            push_to_gcr=$(jq '.GCR' $registries_filename)
          fi
        fi

        echo "push_to_dockerhub=${push_to_dockerhub}" >> "$GITHUB_OUTPUT"
        echo "push_to_gcr=${push_to_gcr}" >> "$GITHUB_OUTPUT"

    - name: Validate version
      run: |
        buidpackTomlVersion=$(sudo skopeo inspect "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" | jq -r '.Labels."io.buildpacks.buildpackage.metadata" | fromjson | .version')
        githubReleaseVersion="${{ steps.event.outputs.tag_full }}"
        if [[ "$buidpackTomlVersion" != "$githubReleaseVersion" ]]; then
          echo "Version in buildpack.toml ($buidpackTomlVersion) and github release ($githubReleaseVersion) are not identical"
          exit 1
        fi

    - name: Push to GCR
      if: ${{  steps.parse_configs.outputs.push_to_gcr == 'true' }}
      env:
        GCR_PUSH_BOT_JSON_KEY: ${{ secrets.GCR_PUSH_BOT_JSON_KEY }}
      run: |
        echo "${GCR_PUSH_BOT_JSON_KEY}" | sudo skopeo login --username _json_key --password-stdin gcr.io
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://gcr.io/${{ github.repository }}:${{ steps.event.outputs.tag_full }}"
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://gcr.io/${{ github.repository }}:${{ steps.event.outputs.tag_minor }}"
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://gcr.io/${{ github.repository }}:${{ steps.event.outputs.tag_major }}"
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://gcr.io/${{ github.repository }}:latest"

    - name: Push to DockerHub
      if: ${{  steps.parse_configs.outputs.push_to_dockerhub == 'true' }}
      id: push
      env:
        DOCKERHUB_USERNAME: ${{ secrets.PAKETO_BUILDPACKS_DOCKERHUB_USERNAME }}
        DOCKERHUB_PASSWORD: ${{ secrets.PAKETO_BUILDPACKS_DOCKERHUB_PASSWORD }}
        GITHUB_REPOSITORY_OWNER: ${{ github.repository_owner }}
      run: |
        REPOSITORY="${GITHUB_REPOSITORY_OWNER/-/}/${GITHUB_REPOSITORY#${GITHUB_REPOSITORY_OWNER}/}" # translates 'paketo-buildpacks/bundle-install' to 'paketobuildpacks/bundle-install'
        IMAGE="index.docker.io/${REPOSITORY}"
        echo "${DOCKERHUB_PASSWORD}" | sudo skopeo login --username "${DOCKERHUB_USERNAME}" --password-stdin index.docker.io
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://${IMAGE}:${{ steps.event.outputs.tag_full }}"
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://${IMAGE}:${{ steps.event.outputs.tag_minor }}"
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://${IMAGE}:${{ steps.event.outputs.tag_major }}"
        sudo skopeo copy "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" "docker://${IMAGE}:latest"
        echo "image=${IMAGE}" >> "$GITHUB_OUTPUT"
        echo "digest=$(sudo skopeo inspect "oci-archive:${GITHUB_WORKSPACE}/buildpackage.cnb" | jq -r .Digest)" >> "$GITHUB_OUTPUT"

    - name: Register with CNB Registry
      uses: docker://ghcr.io/buildpacks/actions/registry/request-add-entry:main
      with:
        id: ${{ github.repository }}
        version: ${{ steps.event.outputs.tag_full }}
        address: ${{ steps.push.outputs.image }}@${{ steps.push.outputs.digest }}
        token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}

  failure:
    name: Alert on Failure
    runs-on: ubuntu-24.04
    needs: [push]
    if: ${{ always() && needs.push.result == 'failure' }}
    steps:
    - name: File Failure Alert Issue
      uses: paketo-buildpacks/github-config/actions/issue/file@main
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        repo: ${{ github.repository }}
        label: "failure:push"
        comment_if_exists: true
        issue_title: "Failure: Push Buildpackage workflow"
        issue_body: |
          Push Buildpackage workflow [failed](https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}).
        comment_body: |
           Another failure occurred: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
