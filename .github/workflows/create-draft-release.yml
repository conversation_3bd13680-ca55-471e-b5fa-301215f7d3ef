name: Create or Update Draft Release

on:
  push:
    branches:
    - main
  repository_dispatch:
    types: [ version-bump ]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version of the release to cut (e.g. 1.2.3)'
        required: false

concurrency: release

jobs:
  builders:
    name: Get Builders for Testing
    runs-on: ubuntu-24.04
    outputs:
      builders: ${{ steps.builders.outputs.builders }}
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Get builders from integration.json
      id: builders
      run: |
        source "${{ github.workspace }}/scripts/.util/builders.sh"

        builders="$(util::builders::list "${{ github.workspace }}/integration.json")"
        printf "Output: %s\n" "${builders}"
        printf "builders=%s\n" "${builders}" >> "$GITHUB_OUTPUT"
  integration:
    name: Integration Tests
    runs-on: ubuntu-24.04
    needs: [builders]
    strategy:
      matrix:
        builder: ${{ fromJSON(needs.builders.outputs.builders) }}
      fail-fast: false  # don't cancel all test jobs when one fails
    steps:
    - name: Setup Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'
    - name: Checkout
      uses: actions/checkout@v4
    - name: Run Integration Tests
      env:
        TMPDIR: "${{ runner.temp }}"
      run: ./scripts/integration.sh --builder ${{ matrix.builder }}

  release:
    name: Release
    runs-on: ubuntu-24.04
    needs: integration
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-tags: true
    - name: Reset Draft Release
      id: reset
      uses: paketo-buildpacks/github-config/actions/release/reset-draft@main
      with:
        repo: ${{ github.repository }}
        token: ${{ github.token }}
    - name: Calculate Semver Tag
      if: github.event.inputs.version == ''
      id: semver
      uses: paketo-buildpacks/github-config/actions/tag/calculate-semver@main
      with:
        repo: ${{ github.repository }}
        token: ${{ github.token }}
        ref-name: ${{ github.ref_name }}
    - name: Set Release Tag
      id: tag
      run: |
        tag="${{ github.event.inputs.version }}"
        if [ -z "${tag}" ]; then
          tag="${{ steps.semver.outputs.tag }}"
        fi
        echo "tag=${tag}" >> "$GITHUB_OUTPUT"
    - name: Package
      run: ./scripts/package.sh --version "${{ steps.tag.outputs.tag }}"
    - name: Create Release Notes
      id: create-release-notes
      uses: paketo-buildpacks/github-config/actions/release/notes@main
      with:
        repo: ${{ github.repository }}
        token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}
    - name: Create Draft Release
      uses: paketo-buildpacks/github-config/actions/release/create@main
      with:
        repo: ${{ github.repository }}
        token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}
        tag_name: v${{ steps.tag.outputs.tag }}
        target_commitish: ${{ github.sha }}
        name: v${{ steps.tag.outputs.tag }}
        body: ${{ steps.create-release-notes.outputs.release_body }}
        draft: true
        assets: |
          [
            {
              "path": "build/buildpackage.cnb",
              "name": "${{ github.event.repository.name }}-${{ steps.tag.outputs.tag }}.cnb",
              "content_type": "application/x-tar"
            }
          ]

  failure:
    name: Alert on Failure
    runs-on: ubuntu-24.04
    needs: [ integration, release ]
    if: ${{ always() && needs.integration.result == 'failure' || needs.release.result == 'failure' }}
    steps:
    - name: File Failure Alert Issue
      uses: paketo-buildpacks/github-config/actions/issue/file@main
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        repo: ${{ github.repository }}
        label: "failure:release"
        comment_if_exists: true
        issue_title: "Failure: Create Draft Release workflow"
        issue_body: |
          Create Draft Release workflow [failed](https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}).
        comment_body: |
           Another failure occurred: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
