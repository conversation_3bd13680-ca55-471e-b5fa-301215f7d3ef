name: <PERSON>t Workflows

on:
  pull_request:
    paths:
    - '.github/**.yml'
    - '.github/**.yaml'

jobs:
  lintYaml:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: Checkout github-config
      uses: actions/checkout@v4
      with:
        repository: paketo-buildpacks/github-config
        path: github-config

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: 3.8

    - name: Install yamllint
      run: pip install yamllint

    - name: Lint YAML files
      run: yamllint ./.github -c github-config/.github/.yamllint
