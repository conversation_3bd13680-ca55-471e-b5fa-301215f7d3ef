name: Update shared github-config

on:
  schedule:
  - cron: '20 17 * * *' # daily at 17:20 UTC
  workflow_dispatch: {}

concurrency: github_config_update

jobs:
  build:
    name: Create PR to update shared files
    runs-on: ubuntu-24.04
    steps:

    - name: Checkout
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}

    - name: Checkout github-config
      uses: actions/checkout@v4
      with:
        repository: paketo-buildpacks/github-config
        path: github-config

    - name: Checkout Branch
      uses: paketo-buildpacks/github-config/actions/pull-request/checkout-branch@main
      with:
        branch: automation/github-config/update

    - name: Run the sync action
      uses: paketo-buildpacks/github-config/actions/sync@main
      with:
        workspace: /github/workspace
        config: /github/workspace/github-config/language-family

    - name: Cleanup
      run: rm -rf github-config

    - name: Commit
      id: commit
      uses: paketo-buildpacks/github-config/actions/pull-request/create-commit@main
      with:
        message: "Updating github-config"
        pathspec: "."
        keyid: ${{ secrets.PAKETO_BOT_GPG_SIGNING_KEY_ID }}
        key: ${{ secrets.PAKETO_BOT_GPG_SIGNING_KEY }}

    - name: Push Branch
      if: ${{ steps.commit.outputs.commit_sha != '' }}
      uses: paketo-buildpacks/github-config/actions/pull-request/push-branch@main
      with:
        branch: automation/github-config/update

    - name: Open Pull Request
      if: ${{ steps.commit.outputs.commit_sha != '' }}
      uses: paketo-buildpacks/github-config/actions/pull-request/open@main
      with:
        token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}
        title: "Updates github-config"
        branch: automation/github-config/update

  failure:
    name: Alert on Failure
    runs-on: ubuntu-24.04
    needs: [build]
    if: ${{ always() && needs.build.result == 'failure' }}
    steps:
    - name: File Failure Alert Issue
      uses: paketo-buildpacks/github-config/actions/issue/file@main
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        repo: ${{ github.repository }}
        label: "failure:update-github-config"
        comment_if_exists: true
        issue_title: "Failure: Update GitHub config workflow"
        issue_body: |
          Update GitHub config workflow [failed](https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}).
        comment_body: |
           Another failure occurred: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
