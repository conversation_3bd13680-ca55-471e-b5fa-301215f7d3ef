name: Test Pull Request

on:
  pull_request:
    branches:
    - main

concurrency:
  # only one instance of test suite per PR at one time
  group: pr-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  builders:
    name: Get Builders for Testing
    runs-on: ubuntu-24.04
    outputs:
      builders: ${{ steps.builders.outputs.builders }}
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Get builders from integration.json
      id: builders
      run: |
        source "${{ github.workspace }}/scripts/.util/builders.sh"

        builders="$(util::builders::list "${{ github.workspace }}/integration.json")"
        printf "Output: %s\n" "${builders}"
        printf "builders=%s\n" "${builders}" >> "$GITHUB_OUTPUT"

  integration:
    name: Integration Tests with Builders
    runs-on: ubuntu-24.04
    needs: [builders]
    strategy:
      matrix:
        builder: ${{ fromJSON(needs.builders.outputs.builders) }}
      fail-fast: false  # don't cancel all test jobs when one fails
    steps:
    - name: Setup Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Checkout
      uses: actions/checkout@v4

    - name: Run Integration Tests
      env:
        TMPDIR: "${{ runner.temp }}"
      run: ./scripts/integration.sh --builder ${{ matrix.builder }}

  roundup:
    name: Integration Tests
    if: ${{ always() }}
    runs-on: ubuntu-24.04
    needs: integration
    steps:
    - run: |
        result="${{ needs.integration.result }}"
        if [[ $result == "success" ]]; then
          echo "Integration tests passed against all builders"
          exit 0
        else
          echo "Integration tests failed on one or more builders"
          exit 1
        fi

  upload:
    name: Upload Workflow Event Payload
    runs-on: ubuntu-24.04
    steps:
    - name: Upload Artifact
      uses: actions/upload-artifact@v4
      with:
        name: event-payload
        path: ${{ github.event_path }}
