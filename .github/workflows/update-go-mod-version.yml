name: Update Go version

on:
  schedule:
    - cron: '13 4 * * MON'  # every monday at 4:13 UTC
  workflow_dispatch:

concurrency: update-go

jobs:
  update-go:
    name: Update go toolchain in go.mod
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Checkout PR Branch
        uses: paketo-buildpacks/github-config/actions/pull-request/checkout-branch@main
        with:
          branch: automation/go-mod-update/update-main
      - name: Setup Go
        id: setup-go
        uses: actions/setup-go@v5
        with:
          go-version: 'stable'
      - name: Get current go toolchain version
        id: current-go-version
        uses: paketo-buildpacks/github-config/actions/update-go-mod-version@main
        with:
          go-version: ${{ steps.setup-go.outputs.go-version }}
      - name: Go mod tidy
        run: |
          #!/usr/bin/env bash
          set -euo pipefail
          shopt -s inherit_errexit

          echo "Before running go mod tidy"
          echo "head -n10 go.mod "
          head -n10 go.mod

          echo "git diff"
          git diff

          echo "Running go mod tidy"
          go mod tidy

          echo "After running go mod tidy"
          echo "head -n10 go.mod "
          head -n10 go.mod

          echo "git diff"
          git diff
      - name: Commit
        id: commit
        uses: paketo-buildpacks/github-config/actions/pull-request/create-commit@main
        with:
          message: "Updates go mod version to ${{ steps.setup-go.outputs.go-version }}"
          pathspec: "."
          keyid: ${{ secrets.PAKETO_BOT_GPG_SIGNING_KEY_ID }}
          key: ${{ secrets.PAKETO_BOT_GPG_SIGNING_KEY }}

      - name: Push Branch
        if: ${{ steps.commit.outputs.commit_sha != '' }}
        uses: paketo-buildpacks/github-config/actions/pull-request/push-branch@main
        with:
          branch: automation/go-mod-update/update-main

      - name: Open Pull Request
        if: ${{ steps.commit.outputs.commit_sha != '' }}
        uses: paketo-buildpacks/github-config/actions/pull-request/open@main
        with:
          token: ${{ secrets.PAKETO_BOT_GITHUB_TOKEN }}
          title: "Updates go mod version to ${{ steps.setup-go.outputs.go-version }}"
          branch: automation/go-mod-update/update-main

  failure:
    name: Alert on Failure
    runs-on: ubuntu-24.04
    needs: [update-go]
    if: ${{ always() && needs.update-go.result == 'failure' }}
    steps:
      - name: File Failure Alert Issue
        uses: paketo-buildpacks/github-config/actions/issue/file@main
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repo: ${{ github.repository }}
          label: "failure:update-go-version"
          comment_if_exists: true
          issue_title: "Failure: Update Go Mod Version workflow"
          issue_body: |
            Update Go Mod Version workflow [failed](https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}).
          comment_body: |
            Another failure occurred: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
