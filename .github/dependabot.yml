---
version: 2
updates:
- package-ecosystem: gomod
  directory: "/"
  schedule:
    interval: daily
  allow:
  # Allow both direct and indirect updates for all packages
  - dependency-type: "all"
  # group all minor and patch dependency updates together
  groups:
    go-modules:
      patterns:
      - "*"
      update-types:
      - "minor"
      - "patch"
      exclude-patterns:
      - "github.com/anchore/stereoscope"
      - "github.com/testcontainers/testcontainers-go"
      - "github.com/docker/docker"
      - "github.com/containerd/containerd"
