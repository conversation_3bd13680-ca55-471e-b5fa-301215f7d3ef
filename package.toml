
[buildpack]
  uri = "build/buildpack.tgz"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/ca-certificates@3.10.3"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/environment-variables@4.9.2"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/git@1.0.61"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/go-build@2.2.40"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/go-dist@2.7.14"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/go-mod-vendor@1.0.69"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/image-labels@4.10.1"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/procfile@5.11.2"

[[dependencies]]
  uri = "urn:cnb:registry:paketo-buildpacks/watchexec@3.5.3"
